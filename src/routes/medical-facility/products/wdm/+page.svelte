<script lang="ts">
	import Bg from '$lib/assets/images/wow/bg2.webp'

	import Banner from "$lib/components/Banner.svelte";

	import LogoSynektik from '$lib/assets/images/logo-synektik-blue.svg';

	import LogoWdm from '$lib/assets/images/logo_wdm_new.svg';
	import ImgWdm from '$lib/assets/images/wow/image-features-placeholder.jpg'

	import IllustrationFinance from '$lib/assets/images/illustration-finance.svg';
	import ImgScreenshot from "$lib/assets/images/wow/image-screenshot.jpg";

	import TeamMa from '$lib/assets/images/team-ma.png';
	import TeamTo from '$lib/assets/images/team-to.png';
	import TeamZu from '$lib/assets/images/team-zu.png';

	import LogoSyndose from '$lib/assets/images/logo_syndose_new.svg';
	import LogoWOW from '$lib/assets/images/logo_wow_new.svg';
	import LogoAI from '$lib/assets/images/logo_ai_new.svg';
	import LogoPP from '$lib/assets/images/logo_pp_new.svg';

    import Breadcrumbs from "$lib/components/Breadcrumbs.svelte";
    import WdmAccordion from "$lib/components/WdmAccordion.svelte";
    import Carousel from '$lib/components/Carousel.svelte';
    import { onMount, onDestroy } from 'svelte';

	function showContactFirst() {
		var scfEl = document.getElementById("showContactFirst");
		var scfBtn = document.getElementById("showContactFirstButton");
		if(scfEl) {
			scfEl.style.display = "block";
			if(scfBtn) {
				scfBtn.remove();
			}
			scfEl.innerHTML='<a class="btn" href="mailto:<EMAIL>"><EMAIL></a>';
		}
	}

    // Sticky header functionality
    function initStickyHeader() {
        const stickyHeader = document.querySelector('.wdm-sticky-header') as HTMLElement;
        const comparisonSection = document.querySelector('.wdm-comparison') as HTMLElement;
        const featureItems = document.querySelectorAll('.wdm-feature-item');
        
        if (!stickyHeader || !comparisonSection) return;
        
        let isSticky = false;
        let ticking = false;
        let isMobile = window.innerWidth < 768;
        
        // Measure exact header dimensions to prevent jumps
        let headerHeight = stickyHeader.offsetHeight;
        let headerMarginBottom = parseInt(window.getComputedStyle(stickyHeader).marginBottom);
        let totalHeaderSpace = headerHeight + headerMarginBottom;
        
        // Function to check if mobile
        function checkMobile() {
            isMobile = window.innerWidth < 768;
        }
        
        // Add mobile labels
        function addMobileLabels() {
            if (!isMobile) return;
            
            const labels = ['Automatyczna', 'Manualne', 'Lista'];
            
            featureItems.forEach(item => {
                const values = item.querySelectorAll('.wdm-feature-value');
                values.forEach((value, index) => {
                    const htmlValue = value as HTMLElement;
                    if (index < labels.length) {
                        // Check if label already exists
                        const existingLabel = value.querySelector('.mobile-label');
                        if (!existingLabel) {
                            const label = document.createElement('div');
                            label.className = 'mobile-label';
                            label.style.cssText = 'font-weight: 600; color: #1e40af; margin-bottom: 4px; font-size: 0.875rem;';
                            label.textContent = labels[index];
                            value.insertBefore(label, value.firstChild);
                        }
                        
                        // Make text left-aligned on mobile
                        htmlValue.style.textAlign = 'left';
                    }
                });
            });
        }
        
        // Remove mobile labels
        function removeMobileLabels() {
            const mobileLabels = document.querySelectorAll('.mobile-label');
            mobileLabels.forEach(label => label.remove());
            
            // Reset text alignment
            const values = document.querySelectorAll('.wdm-feature-value');
            values.forEach(value => {
                (value as HTMLElement).style.textAlign = '';
            });
        }
        
        // Add padding to prevent content jump - use exact measurements
        function addPaddingToSection() {
            if (!isSticky) return;
            comparisonSection.style.paddingTop = `${totalHeaderSpace}px`;
        }
        
        // Remove padding when not sticky
        function removePaddingFromSection() {
            comparisonSection.style.paddingTop = '0';
        }
        
        // Handle sticky behavior
        function handleSticky() {
            if (!stickyHeader || !comparisonSection) return;
            
            // Handle mobile view
            if (isMobile) {
                if (isSticky) {
                    isSticky = false;
                    stickyHeader.classList.remove('stuck');
                    stickyHeader.style.position = 'relative';
                    stickyHeader.style.top = 'auto';
                    removePaddingFromSection();
                }
                addMobileLabels();
                return;
            }
            
            // Desktop view - remove mobile labels
            removeMobileLabels();
            
            // Get section boundaries
            const sectionRect = comparisonSection.getBoundingClientRect();
            const sectionTop = sectionRect.top;
            const sectionBottom = sectionRect.bottom;
            
            // Check if we're scrolling within the section
            const shouldBeSticky = sectionTop <= 0 && sectionBottom > headerHeight;
            
            // Apply changes only when state actually changes to avoid glitches
            if (shouldBeSticky && !isSticky) {
                // First add padding to prevent jump
                comparisonSection.style.paddingTop = `${totalHeaderSpace}px`;
                
                // Then make header sticky
                isSticky = true;
                stickyHeader.classList.add('stuck');
                
                // Apply fixed styles
                stickyHeader.style.position = 'fixed';
                stickyHeader.style.top = '0';
                stickyHeader.style.left = '0';
                stickyHeader.style.right = '0';
                stickyHeader.style.width = '100%';
                stickyHeader.style.zIndex = '100';
                stickyHeader.style.backgroundColor = 'white';
                stickyHeader.style.borderRadius = '0';
                stickyHeader.style.borderBottom = '1px solid #BECDF8';
                stickyHeader.style.borderTop = 'none';
                stickyHeader.style.borderLeft = 'none';
                stickyHeader.style.borderRight = 'none';
                stickyHeader.style.padding = '1rem';
                stickyHeader.style.marginLeft = 'calc(-50vw + 50%)';
                stickyHeader.style.marginBottom = '0';
            } else if (!shouldBeSticky && isSticky) {
                isSticky = false;
                stickyHeader.classList.remove('stuck');
                
                // First reset styles
                stickyHeader.style.cssText = '';
                stickyHeader.className = 'wdm-sticky-header mb-6 hidden md:flex w-full gap-8 bg-white py-4 px-4 rounded-xl border border-[#BECDF8] ';
                
                // Then remove padding
                removePaddingFromSection();
            }
        }
        
        // Smooth scroll handler
        function onScroll() {
            requestAnimationFrame(() => {
                handleSticky();
            });
        }
        
        // Throttled resize handler
        function onResize() {
            if (!ticking) {
                requestAnimationFrame(() => {
                    // Recalculate header dimensions on resize
                    const newHeaderHeight = stickyHeader.offsetHeight;
                    const newHeaderMarginBottom = parseInt(window.getComputedStyle(stickyHeader).marginBottom);
                    
                    if (newHeaderHeight !== headerHeight) {
                        headerHeight = newHeaderHeight;
                        headerMarginBottom = newHeaderMarginBottom;
                        totalHeaderSpace = headerHeight + headerMarginBottom;
                    }
                    
                    checkMobile();
                    handleSticky();
                    ticking = false;
                });
                ticking = true;
            }
        }
        
        // Initialize
        checkMobile();
        handleSticky();
        
        // Add event listeners
        window.addEventListener('scroll', onScroll, { passive: true });
        window.addEventListener('resize', onResize, { passive: true });
        
        // Cleanup function
        return () => {
            window.removeEventListener('scroll', onScroll);
            window.removeEventListener('resize', onResize);
        };
    }
	
	// Initialize on mount
	let cleanup: (() => void) | undefined;
	
	onMount(() => {
		// Small delay to ensure DOM is fully rendered
		setTimeout(() => {
			cleanup = initStickyHeader();
		}, 100);
	});
	
	onDestroy(() => {
		if (cleanup) {
			cleanup();
		}
	});



</script>

<svelte:head>
	<title>Zbadani.pl | WDM - Współdzielenie Dokumentacji Medycznej</title>
	<meta name="description" content="WDM - Współdzielenie Dokumentacji Medycznej" />
</svelte:head>

<Banner src={Bg}>
	<div slot="bannerContent" class="text-blue-100">
		<Breadcrumbs current="Współdzielenie Dokumentacji Medycznej" previous="Dla placówki" previousUrl="/dla-placowki" />
		<div class="text-center flex mb-4 items-center">
			<img src={LogoWdm} alt="Logo WDM" class="max-h-32">
		</div>
		<p class="mb-4">Współdzielenie Dokumentacji Medycznej (WDM) od Zbadani.pl to nowoczesne narzędzie do wymiany i udostępniania dokumentacji medycznej pomiędzy placówkami diagnostycznymi, obrazującymi i innymi użytkownikami placówek medycznych. Dzięki automatycznemu transferowi zdjęć DICOM i innych plików medycznych w tym opisu, placówki które korzystają z usługi badanie otrzymuje pełne dane, bez konieczności ręcznego przesyłania plików.</p>
		<a href="#skontaktuj-sie-z-nami" class="btn btn-fill mt-4">Zamów prezentację</a>
	</div>
</Banner>

<div class="bg-blue-900">
	<main>
		
		<section id="dla-kogo" class="bg-blue-900 !pb-0">
			<div class="container mx-auto md:flex items-center md:gap-16">
				<div class="md:w-1/2">
					<h3 class="mb-4">Dla kogo jest WDM</h3>
					<p>Nasz system został stworzony z myślą o placówkach:</p>
					<ul class="list-disc list-outside pl-4">
						<li class="py-2">Zlecających badania – aby w prosty sposób otrzymywać pełne zestawy obrazów od pracowni diagnostycznych.</li>
						<li class="py-2">Placówkach, które muszą szybko udostępnić dowolne badanie w celu zapoznania się z nim przez innego pracownika medycznego (zdalnie)</li>
					</ul>
				</div>
				<div class="md:w-1/2 md:px-8">
					<img src="{ImgScreenshot}" alt="Zrzut ekranu" class="rounded-lg shadow-[0_35px_60px_-15px_rgba(0,42,163,0.1)]">
				</div>
			</div>
		</section>

		<section id="kluczowe-funkcje" class="bg-blue-900">
			<h3 class="mb-12 text-center">Kluczowe funkcje</h3>

			<div class="mx-auto container">
				<div class="md:grid grid-cols-4 grid-rows-6 gap-6 features-grid">
					<div class="row-span-2">
						<h4>Automatyczna wymiana DICOM</h4>
						<p>Automatyzacja pobierania i wysyłki kompletnej dokumentacji badania bez ręcznego określenia co minimalizuje ryzyko pominięcia istotnych plików.</p>
					</div>
					<div class="row-span-2">
						<h4>Szybkość transferu</h4>
						<p>Optymalizowany kanał komunikacji gwarantuje przesył nawet kilkuset zdjęć DICOM w przeciągu minut, co skraca czas oczekiwania na badania.</p>
					</div>
					<div class="row-span-2 bluey">
						<h4>Integracja z PACS i RIS</h4>
						<p>Bezproblemowe połączenie z istniejącymi systemami PACS/RIS, pozwalające utrzymać spójność infrastruktury i uniknąć dublowania danych.</p>
					</div>
					<div class="row-span-2">
						<h4>Współdzielenie na określony numer telefonu</h4>
						<p>umożliwia współdzielenie badania wykonanego w placówce z lekarzem lub specjalistą w innej placówce w celu dodania komentarza eksperckiego, przy zachowaniu pełnej kontroli nad dostępem i bezpieczeństwem danych wrażliwych. (anominizacja)</p>
					</div>
					<div class="row-span-2 col-start-1 row-start-3 bluey">
						<h4>Zaawansowane zarządzanie uprawnieniami</h4>
						<p>Elastyczny system ról i zezwoleń pozwala definiować, kto, kiedy i w jakim zakresie może przeglądać lub pobierać dokumentację medyczną.</p>
					</div>
					<div class="row-span-2 col-start-2 row-start-3">
						<h4>Bezpieczeństwo i zgodność z RODO</h4>
						<p>Zastosowane mechanizmy współdzielenia badań w zależności od wybranej opcji w ramach usługi WDM, zapewniając zgodność z przepisami o ochronie danych osobowych oraz standardami bezpieczeństwa w ochronie zdrowia.</p>
					</div>
					<div class="row-span-2 col-start-3 row-start-3">
						<h4>Monitorowanie i audyt</h4>
						<p>Szczegółowe logi wszystkich operacji — od wysyłki po pobranie plików — umożliwiają szybkie sprawdzenie, kto i kiedy uzyskał dostęp do dokumentów i jakie akcje zostały wykonane na danym badaniu.</p>
					</div>
					<div class="row-span-2 col-start-4 row-start-3 bluey">
						<h4>Wsparcie wieloplacówkowe</h4>
						<p>Możliwość stworzenia dedykowanej sieci współpracy pomiędzy dowolną liczbą placówek, bez ograniczeń co do lokalizacji geograficznej.</p>
					</div>
					<div class="row-span-2 col-start-1 row-start-5">
						<h4>Raportowanie transferów</h4>
						<p>Automatyczne generowanie raportów miesięcznych i ad hoc, pozwalających ocenić obciążenie łącza, czas odpowiedzi oraz liczbę przeprowadzonych transakcji.</p>
					</div>
					<div class="col-span-3 row-span-2 col-start-2 row-start-5 bluey">
						<h4>Dedykowany portal lekarza odbierającego badanie</h4>
						<p>Lekarz, który otrzymuje dostęp do badania ma możliwość zalogowania się na indywidualne konto – portal lekarza w którym będzie miał dostępny opis, obraz DICOM z przeglądarką diagnostyczną i nie tylko.</p>
					</div>
				</div>
			</div>			

		</section>
		
		<section id="tryby-wdm" class="bg-white">

			<div class="md:flex flex-col mx-auto container">
				<div class="mx-auto text-center relative mb-12">
					<h2 class="prehead">Tryby WDM</h2>
					<h3 class="mb-8">Porównanie trybów usługi</h3>
				</div>
				
				<div class="wdm-comparison">
					<!-- Sticky Header -->
					<div class="wdm-sticky-header mb-6 hidden md:flex w-full gap-8 bg-white py-4 px-4 rounded-xl border border-[#BECDF8]">
						<div class="w-full text-center">
							<h4 class="text-blue-100 font-semibold">Automatyczna</h4>
						</div>
						<div class="w-full text-center">
							<h4 class="text-blue-100 font-semibold">Manualne</h4>
						</div>
						<div class="w-full text-center">
							<h4 class="text-blue-100 font-semibold">Lista</h4>
						</div>
					</div>

					<!-- Features -->
					<div class="space-y-4">
						<div class="wdm-feature-item">
							<div class="wdm-feature-header">
								<h4>Opis trybu działania</h4>
							</div>
							<div class="wdm-feature-content">
								<div class="wdm-feature-value">Na podstawie nr REGON z komunikacji HL7. Idealne rozwiązanie dla współdzielenia badań pomiędzy placówką obrazującą a zlecającą.</div>
								<div class="wdm-feature-value">Współdzielenia dokumentacji medycznej z zewnętrznymi użytkownikami, którzy są definiowani na podstawie numeru telefonu.</div>
								<div class="wdm-feature-value">Placówka na etapie wdrożenia podaje listę, która jest wprowadzana i dostępna z poziomu modułu WDM.</div>
							</div>
						</div>

						<div class="wdm-feature-item">
							<div class="wdm-feature-header">
								<h4>Udostepnianie pomiędzy</h4>
							</div>
							<div class="wdm-feature-content">
								<div class="wdm-feature-value text-center">Placówka obrazująca &rarr; Placówka zlecająca</div>
								<div class="wdm-feature-value text-center">Placówka medyczna &rarr; Pojedyńczy odbiorca</div>
								<div class="wdm-feature-value text-center">Placówka medyczna &rarr; Inna placówka</div>
							</div>
						</div>

						<div class="wdm-feature-item">
							<div class="wdm-feature-header">
								<h4>Określenie odbiorców</h4>
							</div>
							<div class="wdm-feature-content">
								<div class="wdm-feature-value text-center">SERWIS</div>
								<div class="wdm-feature-value text-center">KLIENT</div>
								<div class="wdm-feature-value text-center">SERWIS</div>
							</div>
						</div>

						<div class="wdm-feature-item">
							<div class="wdm-feature-header">
								<h4>Anonimizacja badań</h4>
							</div>
							<div class="wdm-feature-content">
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✕</span>
										</span>
										NIE
									</span>
								</div>
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✓</span>
										</span>
										TAK
									</span>
								</div>
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✓</span>
										</span>
										TAK
									</span>
								</div>
							</div>
						</div>

						<div class="wdm-feature-item">
							<div class="wdm-feature-header">
								<h4>Anonimizacja plików DICOM</h4>
							</div>
							<div class="wdm-feature-content">
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✕</span>
										</span>
										NIE
									</span>
								</div>
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✓</span>
										</span>
										TAK
									</span>
								</div>
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✓</span>
										</span>
										TAK
									</span>
								</div>
							</div>
						</div>

						<div class="wdm-feature-item">
							<div class="wdm-feature-header">
								<h4>Powiadomienia e-mail</h4>
							</div>
							<div class="wdm-feature-content">
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✕</span>
										</span>
										NIE
									</span>
								</div>
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✕</span>
										</span>
										NIE
									</span>
								</div>
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✓</span>
										</span>
										TAK
									</span>
								</div>
							</div>
						</div>

						<div class="wdm-feature-item">
							<div class="wdm-feature-header">
								<h4>Powiadomienia SMS</h4>
							</div>
							<div class="wdm-feature-content">
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✕</span>
										</span>
										NIE
									</span>
								</div>
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✓</span>
										</span>
										TAK
									</span>
								</div>
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✓</span>
										</span>
										TAK
									</span>
								</div>
							</div>
						</div>

						<div class="wdm-feature-item">
							<div class="wdm-feature-header">
								<h4>Automatyczny transfer badań</h4>
							</div>
							<div class="wdm-feature-content">
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✓</span>
										</span>
										TAK
									</span>
								</div>
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✓</span>
										</span>
										TAK
									</span>
								</div>
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✓</span>
										</span>
										TAK
									</span>
								</div>
							</div>
						</div>

						<div class="wdm-feature-item">
							<div class="wdm-feature-header">
								<h4>Możliwość komentowania pomiędzy udostępniajacym a odbiorcą</h4>
							</div>
							<div class="wdm-feature-content">
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✕</span>
										</span>
										NIE
									</span>
								</div>
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✓</span>
										</span>
										TAK
									</span>
								</div>
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✕</span>
										</span>
										NIE
									</span>
								</div>
							</div>
						</div>

						<div class="wdm-feature-item">
							<div class="wdm-feature-header">
								<h4>Opis badania (w postaci PDF i tekstu)</h4>
							</div>
							<div class="wdm-feature-content">
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✓</span>
										</span>
										TAK
									</span>
								</div>
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✓</span>
										</span>
										TAK
									</span>
								</div>
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✓</span>
										</span>
										TAK
									</span>
								</div>
							</div>
						</div>

						<div class="wdm-feature-item">
							<div class="wdm-feature-header">
								<h4>Webowa przeglądarka DICOM</h4>
							</div>
							<div class="wdm-feature-content">
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✓</span>
										</span>
										TAK
									</span>
								</div>
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✓</span>
										</span>
										TAK
									</span>
								</div>
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✓</span>
										</span>
										TAK
									</span>
								</div>
							</div>
						</div>

						<div class="wdm-feature-item">
							<div class="wdm-feature-header">
								<h4>Pobieranie plików DICOM na dysk</h4>
							</div>
							<div class="wdm-feature-content">
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✓</span>
										</span>
										TAK
									</span>
								</div>
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✓</span>
										</span>
										TAK
									</span>
								</div>
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✓</span>
										</span>
										TAK
									</span>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

		</section>

		<section id="co-mowia" class="bg-blue-900">
			<div class="flex mx-auto container">
				<div class="md:w-1/2 mx-auto text-center relative">
					<h2 class="prehead">FAQ</h2>
					<h3 class="mb-8">Najczęściej zadawane pytania</h3>
					<svelte:component this={WdmAccordion} />
				</div>  
			</div>
		</section>

		<section id="dlaczego-warto" class="bg-white">
			<div class="md:flex mx-auto container items-center">
				<div class="md:w-1/2 hidden md:block">
					<img src="{LogoSynektik}" class="h-auto w-full max-w-96 mx-auto" alt="Logo Synektik.com.pl">
				</div>
				<div class="md:w-1/2">
					<h2 class="prehead">Dlaczego warto</h2>
					<h3 class="mb-4">Wybierz oprogramowanie<br /> platformy Zbadani.pl</h3>
					<p>Właścicielem Zbadani.pl jest firma Synektik S.A posiadająca ponad 20 letnie doświadczenie w sektorze medycznym w różnych obszarach.</p>
					<p>Zespół Zbadani.pl:</p>
					<ul class="pl-4 list-disc -mt-2">
						<li>to zespół ludzi, którzy tworzą oprogramowanie z jakim sami chcieliby pracować;</li>
						<li>wspiera merytorycznie, posiadając na swoim pokładzie specjalistów z obszaru fizyki medycznej;</li>
						<li>na bieżąco reaguje na zmiany w polskim prawie i niezwłocznie aktualizuje oprogramowanie;</li>
						<li>konsultuje nowe funkcje że specjalistami i praktykami;</li>
						<li>udoskonala funkcje na podstawie informacji otrzymanych od placówek;</li>
						<li>stale monitoruje przepisy i normy prawne obowiązujące na polskim rynku.</li>
					</ul>
				</div>
			</div>
		</section>

		<section id="finansowanie" class="bg-blue-900">
			<div class="md:flex mx-auto container items-center">
				<div class="md:w-1/2">
					<h2 class="mb-4">Finansowanie</h2>
					<p>Finansowanie z KPO i funduszy unijnych – skorzystaj z możliwości razem z&nbsp;Zbadani.pl</p>
					<p>Realizujesz projekt w obszarze diagnostyki obrazowej? Wykorzystaj wsparcie z&nbsp;Krajowego Programu Odbudowy (KPO) i funduszy unijnych, aby zmodernizować swoje rozwiązania i wprowadzić nowoczesne technologiedo swojej placówki.</p>
					<p>Zespół Zbadani.pl pomoże Ci w całym procesie – od identyfikacji dostępnych źródeł finansowania, przez przygotowanie dokumentacji, aż po wsparcie w&nbsp;realizacji projektu. Nasze doświadczenie w branży i znajomość wymagań formalnych pozwalają maksymalnie zwiększyć Twoje szanse na pozyskanie środków.</p>
					<p>Nie przegap tej okazji! Skontaktuj się z nami i dowiedz się, jak możemy pomóc w&nbsp;realizacji Twojego projektu. Wspólnie zrealizujemy Twoją wizję!</p>
				</div>
				<div class="md:w-1/2 hidden md:block">
					<img class="mx-auto" src="{IllustrationFinance}" alt="Finansowanie Zbadani Synektik">
				</div>
			</div>
		</section>

		<section id="proces" class="bg-white">
			<h2 class="text-center prehead">Jak zacząć</h2>
			<h3 class="text-center">Proces wdrażania usługi</h3>
			<div class="md:flex gap-8 container mx-auto pt-8 text-center">
				<div class="w-full">
					<span class="w-16 h-16 rounded-full bg-blue-900 text-2xl leading-none flex items-center mx-auto mb-4 text-blue-500">
						<h5 class="text-center w-full relative -top-[2px]">1</h5>
					</span>
					<h4 class="text-blue-300 mb-2">Prezentacja<br /> rozwiązania</h4>
					<p>za pomocą narzędzi on-line oraz w placówce. Wstępna chęć nawiązania współpracy.</p>
				</div>

				<div class="w-full">
					<span class="w-16 h-16 rounded-full bg-blue-900 text-2xl leading-none flex items-center mx-auto mb-4 text-blue-500">
						<h5 class="text-center w-full relative -top-[2px]">2</h5>
					</span>
					<h4 class="text-blue-300 mb-2">Weryfikacja<br /> techniczna</h4>
					<p>Sprawdzenie możliwości technicznych i dostarczenie wypełnionego formularza technicznego, po którym następuje jego obustronne potwierdzenie.</p>
				</div>

				<div class="w-full">
					<span class="w-16 h-16 rounded-full bg-blue-900 text-2xl leading-none flex items-center mx-auto mb-4 text-blue-500">
						<h5 class="text-center w-full relative -top-[2px]">3</h5>
					</span>
					<h4 class="text-blue-300 mb-2">Instalacja i&nbsp;procedowanie&nbsp;umowy</h4>
					<p>Proces instalacji może przebiegać na równi z procedowaniem umowy w celu szybszego wdrożenia rozwiązania w placówce.</p>
				</div>

				<div class="w-full">
					<span class="w-16 h-16 rounded-full bg-blue-900 text-2xl leading-none flex items-center mx-auto mb-4 text-blue-500">
						<h5 class="text-center w-full relative -top-[2px]">4</h5>
					</span>
					<h4 class="text-blue-300 mb-2">Opieka<br /> powdrożeniowa</h4>
					<p>Szkolenie zespołu, przekazanie instrukcji i wsparcie wraz z gwarancją.</p>
				</div>
			</div>
		</section>

		<section id="skontaktuj-sie-z-nami" class="bg-blue-900">
			<div class="md:flex container mx-auto">

				<div class="md:w-1/2">
					<h2 class="mb-4">Skontaktuj się z nami</h2>
					<div class="pr-12">
						<p>
							Chcesz dowiedzieć się więcej o tym, jak WDM może wspierać współpracę między placówkami i&nbsp;efektywność w Twojej organizacji? Umów się na prezentację, 
							aby poznać szczegóły dostosowane do potrzeb Twojego zespołu.
						</p>
						<p>	
							Wspólnie dopasujemy rozwiązania, które spełnią najwyższe standardy 
							bezpieczeństwa i przepisów prawa.
						</p>
					</div>
				</div>

				<div class="md:w-1/2 md:flex bg-white p-12 rounded-2xl gap-12 align-middle justify-center items-center">
					<div class="md:w-1/2">
						<h2 class="prehead mb-4">Kontakt z działem<br />sprzedaży</h2>
						<button id="showContactFirstButton" class="btn mb-6 md:mb-0" on:click={showContactFirst}>Pokaż kontakt</button>
						<div id="showContactFirst" class="hidden mb-6 md:mb-0">test</div>
					</div>
					<div class="md:w-1/2">
						<div class="flex">
							<div class="md:ml-auto relative w-32 h-auto">
								<img class="min-w-12 min-h-12 rounded-full border-2 border-white z-30" src="{TeamMa}" alt="Marta">
							</div>
							<div class="-ml-8 relative w-32 h-auto">
								<img class="min-w-12 min-h-12 rounded-full border-2 border-white z-10" src="{TeamZu}" alt="Zuzanna">
							</div>
						</div>
					</div>
				</div>
			</div>
		</section>

		<section id="zobacz-tez" class="bg-white">
			<h2 class="prehead text-center">Zobacz też</h2>
			<h3 class="text-center mb-16">Inne produkty Zbadani.pl</h3>
			<div class="md:flex container mx-auto gap-12 products">
				<div class="w-full flex flex-col items-center text-center gap-4">
					<img src="{LogoSyndose}" alt="SynDose">
					<h4>SynDose<br /> Rejestr Dawek</h4>
					<p>Usprawnij pracę w pracowni diagnostyki obrazowej oraz medycyny nuklearnej.</p>
					<a href="/medical-facility/products/syndose" class="btn">Dowiedz się więcej</a>
				</div>
				<div class="w-full flex flex-col items-center text-center gap-4">
					<img src="{LogoWOW}" alt="WOW">
					<h4>Wydawanie<br /> Online Wyników</h4>
					<p>Przekazywanie badań i opisów radiologicznych bez płyt, papieru i obecności pacjenta.</p>
					<a href="/medical-facility/products/wow" class="btn">Dowiedz się więcej</a>
				</div>
				<div class="w-full flex flex-col items-center text-center gap-4">
					<img src="{LogoAI}" alt="AI">
					<h4>AI dla<br /> Diagnostyki Obrazowej</h4>
					<p>Pakiet narzędzi wspierających pracę radiologa. Najszersza oferta od jednego producenta.</p>
				</div>
				<div class="w-full flex flex-col items-center text-center gap-4">
					<img src="{LogoPP}" alt="Portal Pacjenta">
					<h4>Portal<br /> Pacjenta</h4>
					<p>Rozwiązanie dla pacjentów umożliwiające gromadzenie badań i konsultowanie ich ze specjalistami.</p>
				</div>
			</div>
		</section>

		<Carousel></Carousel>
		
	</main>
</div>

<style lang="postcss">
	.features-grid > div {
		@apply bg-white rounded-2xl p-6;
		box-shadow: 0px 12px 24px 0px rgba(125, 153, 232, 0.08);
	}
	.features-grid div.bluey {
		background: linear-gradient(180deg, #0848FF 0%, #0039D6 100%);
		box-shadow: none;
	}
	.features-grid > div.nopadding {
		@apply p-0 overflow-hidden;
	}

	.features-grid > div.nopadding img {
		height: 100%;
		object-fit: cover;
	}
	.features-grid > div h4 {
		@apply text-blue-300 mb-2;
	}
	.features-grid > div h5 {
		@apply text-blue-300;
	}
	.features-grid > div.bluey h4, .features-grid > div.bluey h5, .features-grid > div.bluey p {
		@apply text-white;
	}
	section ul {
		@apply text-blue-100;
	}
	section p {
		@apply text-blue-100 mb-4;
	}
	section h2 {
		font-size: 2rem;
		@apply text-blue-100;
	}
	section h2.prehead {
		font-size: 1.3125rem;
		@apply text-blue-300;
	}
	section h3 {
		font-size: 2rem;
		@apply text-blue-100;
	}
	section h4 {
		font-size: 1.3125rem;
		@apply text-blue-100 font-normal;
	}
	section {
		@apply py-32;
	}
	
	/* WDM Comparison Styles */
	.wdm-feature-item {
		@apply bg-white border border-[#BECDF8] rounded-xl overflow-hidden;
	}
	
	.wdm-feature-header {
		@apply px-6 py-4 text-left border-b border-[#BECDF8];
	}
	
	.wdm-feature-header h4 {
		@apply text-blue-100 mb-0 text-base;
	}
	
	.wdm-feature-content {
		@apply grid grid-cols-1 md:grid-cols-3;
	}
	
	.wdm-feature-value {
		@apply px-6 py-4 text-blue-100 border-b border-[#BECDF8] md:border-r md:border-b-0 last:border-r-0 last:border-b-0;
	}
	
	/* Sticky Header */
	.wdm-sticky-header {
		position: normal;
		top: 0;
		z-index: 10;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
		transition: all 0.3s ease;
	}
	
	.wdm-sticky-header.stuck {
		box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
	}
	
	#kluczowe-funkcje p {
		@apply mb-0 pb-0;
	}
	@media screen and (max-width: 1024px) {
		section {
			@apply py-8;
		}
		h3 br, h4 br {
			@apply hidden;
		}
		#kluczowe-funkcje div > div {
			@apply mb-6
		}
		.products > div {
			@apply border p-8 rounded-2xl mb-6;
			border: 1px solid #BECDF8;
		}
		
		.wdm-feature-content {
			@apply grid-cols-1;
		}
		
		.wdm-feature-value {
			@apply border-b border-r-0;
		}
	}
</style>
