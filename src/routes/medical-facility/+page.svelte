<script lang="ts">
	import * as m from "$lib/paraglide/messages.js";
	import { languageTag } from "$lib/paraglide/runtime";
	import Banner from "$lib/components/Banner.svelte";
	import Cover from '$lib/assets/images/hero/hero-mf.webp';
	// import MfEcosystem from '$lib/assets/images/mf_ecosystem.svg';
	import MfAccordion from "$lib/components/MfAccordion.svelte";
</script>

<svelte:head>
	<title>Zbadani.pl | {m.forMf()}</title>
	<meta name="description" content="Zbadani.pl dla placówki" />
</svelte:head>


<Banner src={Cover} >
	<div slot="bannerContent" class="text-blue-100">
		<span class="inline-block px-4 py-2 mb-4 bg-blue-900 rounded-lg text-blue-500 font-normal">{m.need_more()}</span>
		<h1 class="text-3xl mb-4 font-medium">{m.service_level_upgrade()}</h1>
		<p>{m.complex_solutions()}</p>
		<a href="#products" class="btn mt-4 mr-4">{m.products_know()}</a>
	</div>
</Banner>

<div class="container mx-auto py-20" id="products">
	<h2 class="text-3xl text-blue-100 font-medium pb-20 text-center">{m.products()}</h2>
	<svelte:component this={MfAccordion} />
</div>

<style lang="postcss">

</style>