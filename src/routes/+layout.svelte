<script lang="ts">
	import { ParaglideJS } from '@inlang/paraglide-sveltekit'
	import { i18n } from '$lib/i18n'
	import { afterNavigate } from '$app/navigation';

	import "../app.css";
	import { page } from "$app/stores";
	import { browser } from '$app/environment';
	import '@fontsource/lexend';
	import '@fontsource/lexend/500.css';
	import '@fontsource/lexend/600.css';
	import '@fontsource/lexend/800.css';
	import '@fontsource/rubik';
	import '@fontsource/rubik/500.css';
	import '@fontsource/rubik/600.css';
	import '@fontsource/rubik/800.css';
	import Header from "$lib/components/Header.svelte";
	import Footer from "$lib/components/Footer.svelte";
	import PopupProfessional from "$lib/components/PopupProfessional.svelte";
	import { onMount } from "svelte";

	const PROFESSIONAL_POPUP_ENABLED = false;
    let showPopupProfessional = false;
    function updateProfessionalGuard() {
        const currentPath = $page.url.pathname;
        const professionalAgreed = browser ? localStorage.getItem('professionalAgreed') || 'false' : 'false';
        showPopupProfessional = (currentPath.startsWith('/dla-placowki')
                    || currentPath.startsWith('/medical-facility'))
                    && professionalAgreed === 'false';
    }
    $: $page.url.pathname, updateProfessionalGuard();
    if (browser) {
        updateProfessionalGuard();
    }

	function applyA11yStyles(
		fontSize: 	boolean,
		dyslexia: 	boolean,
		contrast: 	boolean,
		links: 		boolean,
		motion: 	boolean
	) {
		fontSize ? document.documentElement.classList.add('a11yFontSize') : document.documentElement.classList.remove('a11yFontSize');
		dyslexia ? document.documentElement.classList.add('a11yDyslexia') : document.documentElement.classList.remove('a11yDyslexia');
		contrast ? document.documentElement.classList.add('a11yContrast') : document.documentElement.classList.remove('a11yContrast');
		links ? document.documentElement.classList.add('a11yLinks') 	  : document.documentElement.classList.remove('a11yLinks');
		motion ? document.documentElement.classList.add('a11yMotion') 	  : document.documentElement.classList.remove('a11yMotion');
	}

	function applyNonBreakingSpaces() {
		const elements = document.querySelectorAll('p, h1, h2, h3, h4, h5, h6, span');
		elements.forEach((el) => {
			el.innerHTML = el.innerHTML
			// Replace single-letter "sierotka" with non-breaking space
			.replace(/\b([aiouwzAIUOWZ])\s(?!&nbsp;)/g, '$1&nbsp;')
			// Prevent breaking between a number and a word (e.g., 11 maja)
			.replace(/(\d+)\s([a-zA-ZęóąśłżźćńĘÓĄŚŁŻŹĆŃ]+)/g, '$1&nbsp;$2');
		});
	}

  	onMount(() => {
		//applyNonBreakingSpaces();
		const fontSize = localStorage.getItem('fontSize') === 'true';
		const dyslexia = localStorage.getItem('dyslexia') === 'true';
		const contrast = localStorage.getItem('contrast') === 'true';
		const links    = localStorage.getItem('links')    === 'true';
		const motion    = localStorage.getItem('motion')  === 'true';
		applyA11yStyles(fontSize, dyslexia, contrast, links, motion);
	});
	afterNavigate(() => {
    	//applyNonBreakingSpaces(); // Run on every navigation
  	});
</script>
<ParaglideJS {i18n}>
	<Header />
	<slot/>
	<Footer />
	{#if PROFESSIONAL_POPUP_ENABLED}<PopupProfessional show={showPopupProfessional} />{/if}
</ParaglideJS>