<script lang="ts">
	import * as m from "$lib/paraglide/messages.js";
	import clsx, { type ClassValue } from 'clsx'
	import { twMerge } from 'tailwind-merge';

	import { createAccordion, melt } from '@melt-ui/svelte';
	import { slide } from 'svelte/transition';

	// @ts-ignore
    import SolarAltArrowDownOutline from '~icons/solar/alt-arrow-down-outline'
	// @ts-ignore
    import SolarAltArrowUpOutline from '~icons/solar/alt-arrow-up-outline'

	function cn(...classes: ClassValue[]) {
		return twMerge(clsx(classes))
	}

	let selectedAccordionItem: any = 'item-1';

	const {
		elements: { content, item, trigger, root },
		helpers: { isSelected },
	} = createAccordion({
		defaultValue: 'item-1',
		onValueChange: ({ curr, next }) => {
			selectedAccordionItem = next;
			return next
		},
	});

    interface Item {
        id: string;
        title: string;
        description: string;
    }

	const items: Item[] = [
		{
			id: 'item-1',
			title: 'Jak wyglądają opłaty za skorzystanie z usług?',
			description: 'Opłaty za usługi są ustalane indywidualnie na podstawie przeprowadzonego wywiadu i formularza technicznego. Wycena zależy od liczby urządzeń, ich typów (modalności) oraz ilości wykonywanych badań.'
		},
		{
			id: 'item-2',
			title: 'Jak przebiega proces zamówienia usługi i integracji z systemem placówki?',
			description: 'Na etapie przygotowania do wdrożenia placówka otrzymuje szczegółowe informacje o niezbędnych zasobach do instalacji. Większość instalacji odbywa się na wirtualnych maszynach, dostosowanych do wytycznych. Proces integracji obejmuje instalację konektora, który przetwarza badania na potrzeby produktów oferowanych przez platformę.',
		},
		{
			id: 'item-3',
			title: 'Czy mogę decydować, które badania są wydawane, a które nie?',
			description: 'Tak, podczas wypełniania formularza technicznego można określić, czy chcesz wprowadzić ograniczenia w wydawaniu badań. Dzięki komunikatowi HL7 istnieje możliwość ograniczenia wydawania opisów, np. badań wewnętrznych pacjentów hospitalizowanych.',
		},
		{
			id: 'item-4',
			title: 'Ile trwa wydanie badania pacjentowi?',
			description: 'Obraz DICOM: Udostępniany pacjentowi po zakończeniu przetwarzania badania w systemie PACS oraz transferze danych. Średni czas wynosi 2-4 godziny.<br /><br />Opis badania: Czas zależy od procedur wewnętrznych placówki. Po zarejestrowaniu opisu w systemie HIS/RIS jest on przesyłany na serwer i dostępny dla pacjenta w kilka minut. Powiadomienie o dostępności opisu wysyłane jest SMS-em.<br /><br />Placówka może również ustalić indywidualne opóźnienia w wysyłaniu opisu badania, dostosowując proces do swoich potrzeb przez cały okres współpracy.',
		},
		{
			id: 'item-5',
			title: 'Jaka jest oszczędność w stosunku do korzystania z duplikatora?',
			description: 'Korzystanie z usługi Online Wyników eliminuje wykluczenie cyfrowe, umożliwiając pacjentom dostęp do wyników z dowolnego miejsca i urządzenia. Pozwala to na ograniczenie konieczności ponownych wizyt w placówce, zmniejsza obciążenie personelu i przyspiesza diagnostykę. Obie usługi – Online Wyniki oraz tradycyjny duplikator – mogą wzajemnie się uzupełniać, co zwiększa dostępność wyników dla większej grupy pacjentów.',
		},
	];

	let className = '';
	export { className as class };
</script>	
	
<div>
	<div>
		<div
		class={cn(
			className,
		)}
		{...$root}
		>
		{#each items as { id, title, description }, i}
			<div
			use:melt={$item(id)}
			class="mb-4"
			>
			<h2 class="flex">
				<button
				use:melt={$trigger(id)}
				class={cn(
					'flex flex-1 cursor-pointer',
					'border border-[#BECDF8] rounded-xl hover:border-blue-500 hover:text-blue-500',
					'px-6 pt-6 pb-6',
                    'text-blue-100 bg-white text-left',
                    $isSelected(id) && 'no-underline rounded-b-none border-b-0 hover:border-[#BECDF8]'
				)}
				>
				{title}
                {#if $isSelected(id)}<SolarAltArrowUpOutline class="ml-auto text-2xl" />{:else}<SolarAltArrowDownOutline class="text-2xl ml-auto opacity-25" />{/if}
				</button>
			</h2>
			{#if $isSelected(id)}
				<div
				class={cn(
					'text-[#666] bg-blue-900',
				)}
				use:melt={$content(id)}
				transition:slide
				>
					<div class="px-6 pb-6 text-left bg-white rounded-b-xl border border-[#BECDF8] border-t-0">
						{@html description}
					</div>
				</div>
			{/if}
			</div>
		{/each}
		</div>

	</div>
</div>