<script lang="ts">
    import type { SvelteComponent } from 'svelte';
	import * as m from "$lib/paraglide/messages.js";
	import { languageTag } from "$lib/paraglide/runtime";
	import clsx, { type ClassValue } from 'clsx'
	import { twMerge } from 'tailwind-merge';

	import { createAccordion, melt } from '@melt-ui/svelte';
	import { slide } from 'svelte/transition';

	// @ts-ignore
    import SolarAltArrowDownOutline from '~icons/solar/alt-arrow-down-outline'
	// @ts-ignore
    import SolarAltArrowUpOutline from '~icons/solar/alt-arrow-up-outline'
	// @ts-ignore
    import SolarDocumentTextOutline from '~icons/solar/document-text-outline'
	// @ts-ignore
    import SolarLogin3Outline from '~icons/solar/login-3-outline'

import LogoWWO from '$lib/assets/images/logo_wwo.svg';
import LogoSynDose from '$lib/assets/images/logo_syndose.svg';
import LogoWDM from '$lib/assets/images/logo_wdm_new.svg';

	import MfImg1 from '$lib/assets/images/mf_img_1.jpg'
	import MfImg2 from '$lib/assets/images/mf_img_2.jpg'

	import DoctorImg1 from '$lib/assets/images/doctor_img_1.jpg'
	import DoctorImg2 from '$lib/assets/images/doctor_img_2.jpg'
	import DoctorImg3 from '$lib/assets/images/doctor_img_3.jpg'
	import DoctorImg4 from '$lib/assets/images/doctor_img_4.jpg'


	function cn(...classes: ClassValue[]) {
		return twMerge(clsx(classes))
	}

	let selectedAccordionItem: any = 'item-1';

	const {
		elements: { content, item, trigger, root },
		helpers: { isSelected },
	} = createAccordion({
		defaultValue: 'item-1',
		onValueChange: ({ curr, next }) => {
			selectedAccordionItem = next;
			return next
		},
	});

    interface Item {
        id: string;
        icon: string;
        title: string;
        description: string;
        url: string;
    }

	const items: Item[] = [
		{
			id: 'item-1',
            icon: LogoSynDose,
			title: m.syndose_full(),
			description: m.mf_item1_description(),
			url: '/medical-facility/products/syndose'
		},
		{
			id: 'item-2',
            icon: LogoWWO,
			title: m.wow_full(),
			description: m.mf_item2_description(),
			url: '/medical-facility/products/wow'
		}
	];

	let className = '';
	export { className as class };
</script>	
	
<div class="lg:grid grid-cols-2 gap-20">
	<div class="flex items-center">
		{#if selectedAccordionItem === 'item-1' || selectedAccordionItem == undefined}
			<img width="680" height="500" class="rounded-xl" src={DoctorImg1} alt="SynDose grafika" />
		{/if}
		{#if selectedAccordionItem === 'item-2'}
			<img width="680" height="500" class="rounded-xl" src={DoctorImg3} alt="WWO grafika" />
		{/if}
		{#if selectedAccordionItem === 'item-3'}
			<img width="680" height="500" class="rounded-xl" src={DoctorImg4} alt="WDM grafika" />
		{/if}
	</div>
	<div>
		<div
		class={cn(
			'my-16',
			className,
		)}
		{...$root}
		>
		{#each items as { id, title, description, icon, url }, i}
			<div
			use:melt={$item(id)}
			class="mb-4"
			>
			<h2 class="flex">
				<button
				use:melt={$trigger(id)}
				class={cn(
					'flex flex-1 cursor-pointer items-center',
					'border-2 border-blue-900 rounded-xl hover:border-blue-500 hover:text-blue-500',
					'px-6 pt-6 pb-6',
                    'text-blue-100 font-semibold underline',
                    $isSelected(id) && 'hover:border-transparent no-underline bg-blue-900 rounded-t-xl rounded-b-none'
				)}
				>
                <img src={icon} class="mr-4 max-h-16" alt="Ikona" />
				{title}
                {#if $isSelected(id)}<SolarAltArrowUpOutline class="ml-auto text-2xl" />{:else}<SolarAltArrowDownOutline class="text-2xl ml-auto opacity-25" />{/if}
				</button>
			</h2>
			{#if $isSelected(id)}
				<div
				class={cn(
					'text-blue-100 bg-blue-900 rounded-b-xl',
				)}
				use:melt={$content(id)}
				transition:slide
				>
					<div class="px-6 pb-6">
						{description}
						<br />
						{#if url}<a class="btn inline-block mt-4" href={url}>{m.know_more()}</a>{/if}
					</div>
				</div>
			{/if}
			</div>
		{/each}
		</div>

	</div>
</div>
