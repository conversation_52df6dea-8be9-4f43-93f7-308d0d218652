<script>
  import * as m from "$lib/paraglide/messages.js";
	import { languageTag } from "$lib/paraglide/runtime";
  import { Splide, SplideSlide } from "@splidejs/svelte-splide";
  //import '@splidejs/svelte-splide/css/core';
  import "@splidejs/svelte-splide/css";

  let options = {
    gap: 10,
    rewind: true,
    autoplay: true,
    arrows: true,
    pagination: false,
    speed: 1000,
    easing: "cubic-bezier(0.25, 1, 0.5, 1)",
    breakpoints: {
      640: { perPage: 1 },
      1024: { perPage: 2 },
      1280: { perPage: 4 },
      1440: { perPage: 4 },
      1920: { perPage: 4 },
      3840: { perPage: 4 },
    },
  };

  import LogoMf0 from "$lib/assets/images/logo_mf_0.jpg?enhanced&w=294";
  import LogoMf1 from "$lib/assets/images/logo_mf_1.jpg?enhanced&w=294";
  import LogoMf2 from "$lib/assets/images/logo_mf_2.jpg?enhanced&w=294";
  import LogoMf3 from "$lib/assets/images/logo_mf_3.jpg?enhanced&w=294";
  import LogoMf4 from "$lib/assets/images/logo_mf_4.jpg?enhanced&w=294";
  import LogoMf5 from "$lib/assets/images/logo_mf_5.jpg?enhanced&w=294";
  import LogoMf6 from "$lib/assets/images/logo_mf_6.jpg?enhanced&w=294";
  import LogoMf7 from "$lib/assets/images/logo_mf_7.jpg?enhanced&w=294";
  import LogoMf8 from "$lib/assets/images/logo_mf_8.jpg?enhanced&w=294";
  import LogoMf9 from "$lib/assets/images/logo_mf_9.jpg?enhanced&w=294";
  import LogoMf10 from "$lib/assets/images/logo_mf_10.jpg?enhanced&w=294";

  const mfLogos = [
    { src: LogoMf0, captionName: "Medicover", captionCity: "" },
    { src: LogoMf1, captionName: "115 Szpital Wojskowy", captionCity: "Hel" },
    { src: LogoMf2, captionName: "NZOZ Morena", captionCity: "Gdańsk" },
    {
      src: LogoMf4,
      captionName:
        "Nowe Techniki Medyczne<br />Szpital Specjalistyczny im. Świętej Rodziny",
      captionCity: "Rudna Mała",
    },
    {
      src: LogoMf5,
      captionName: "Polkowickie Centrum Usług Zdrowotnych",
      captionCity: "Polkowice",
    },
    { src: LogoMf6, captionName: "Quadia", captionCity: "Piaseczno" },
    {
      src: LogoMf7,
      captionName: "SPZOZ MOZ Zielonka",
      captionCity: "Zielonka",
    },
    {
      src: LogoMf8,
      captionName: "SPZOZ Warszawa Wola - Śródmieście",
      captionCity: "Warszawa",
    },
    {
      src: LogoMf9,
      captionName: "SPZOZ Ostrów Mazowiecka",
      captionCity: "Ostrów Mazowiecka",
    },
    {
      src: LogoMf10,
      captionName: "Szpital Powiatowy im. Jana Mikulicza",
      captionCity: "Biskupiec",
    },
    {
      src: LogoMf3,
      captionName: "Ventriculus Leszczyńskie Centrum Medyczne",
      captionCity: "Leszno",
    },
  ];
</script>

<div class="container mx-auto zb-splide-wrapper mt-12 mb-16 mix-blend-darken">
  
  <h3 class="text-center text-blue-300 text-xl font-normal mb-8">
    {m.we_are_in_mf()}
  </h3>

  <Splide {options} aria-label="Client logos">
    {#each mfLogos as logo, index}
      <SplideSlide key={index}>
        <enhanced:img class="logop" src={logo.src} alt="Logo placówki medycznej" />
        <p>{@html logo.captionName}</p>
        <p>{logo.captionCity}</p>
      </SplideSlide>
    {/each}
  </Splide>
</div>

<style lang="postcss">
  .zb-splide-wrapper .logop {
    @apply h-auto max-w-full block mx-auto text-center;
  }

  .zb-splide-wrapper p {
    @apply text-center text-blue-300 text-sm leading-tight block;
  }

  .zb-splide-wrapper p {
    @apply mt-4;
  }

  .zb-splide-wrapper p:last-child {
    @apply mt-0 opacity-50;
  }
</style>
