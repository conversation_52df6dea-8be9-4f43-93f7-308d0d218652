<script lang="ts">
  import * as m from "$lib/paraglide/messages.js";
  
  // @ts-ignore
  import SolarLogin3Outline from "~icons/solar/login-3-outline";
  // @ts-ignore
  import SolarHealthOutline from "~icons/solar/health-outline";
  // @ts-ignore
  import SolarDocumentTextOutline from "~icons/solar/document-text-outline";
  // @ts-ignore
  import SolarLinkOutline from "~icons/solar/link-outline";
  // @ts-ignore
  import SolarCloseCircleOutline from "~icons/solar/close-circle-outline";

  export let show: boolean = false;

  const closeModal = () => {
    show = false;
  };

  const handleBackdropClick = (event: MouseEvent) => {
    if (event.target === event.currentTarget) {
      closeModal();
    }
  };

  const loginOptions = [
    {
      title: m.login_modal_diag_title(),
      description: m.login_modal_diag_desc(),
      url: "https://diagnostyka.zbadani.pl",
      icon: SolarLogin3Outline,
      color: "bg-blue-500"
    },
    {
      title: m.login_modal_syndose_title(),
      description: m.login_modal_syndose_desc(),
      url: "https://syndose.zbadani.pl",
      icon: SolarHealthOutline,
      color: "bg-green-500"
    },
    {
      title: m.login_modal_wdm_title(),
      description: m.login_modal_wdm_desc(),
      url: "https://wdm.zbadani.pl",
      icon: SolarDocumentTextOutline,
      color: "bg-purple-500"
    },
    {
      title: m.login_modal_link_title(),
      description: m.login_modal_link_desc(),
      url: "https://link.zbadani.pl",
      icon: SolarLinkOutline,
      color: "bg-orange-500"
    }
  ];
</script>

{#if show}
  <div 
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4"
    on:click={handleBackdropClick}
    on:keydown={(e) => e.key === 'Escape' && closeModal()}
    role="dialog"
    aria-modal="true"
    aria-labelledby="modal-title"
  >
    <div class="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
      <!-- Header -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <h2 id="modal-title" class="text-2xl font-semibold text-blue-900">
          {m.login_modal_title()}
        </h2>
        <button 
          on:click={closeModal}
          class="text-gray-400 hover:text-gray-600 transition-colors"
          aria-label="Zamknij"
        >
          <SolarCloseCircleOutline class="text-2xl" />
        </button>
      </div>

      <!-- Content -->
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {#each loginOptions as option}
            <a 
              href={option.url}
              target="_blank"
              class="group block p-6 bg-white border-2 border-gray-200 rounded-xl hover:border-blue-500 hover:shadow-lg transition-all duration-300 text-center"
              on:click={closeModal}
            >
              <div class="flex flex-col items-center space-y-4">
                <div class="w-16 h-16 {option.color} rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <svelte:component this={option.icon} class="text-2xl text-white" />
                </div>
                <div>
                  <h3 class="font-semibold text-blue-900 group-hover:text-blue-500 transition-colors">
                    {option.title}
                  </h3>
                  <p class="text-sm text-gray-600 mt-1">
                    {option.description}
                  </p>
                </div>
              </div>
            </a>
          {/each}
        </div>
      </div>
    </div>
  </div>
{/if}

<style lang="postcss">
  /* Responsive adjustments for mobile */
  @media (max-width: 768px) {
    .grid {
      @apply grid-cols-1 gap-3;
    }
    
    .group {
      @apply p-4;
    }
    
    .w-16.h-16 {
      @apply w-12 h-12;
    }
  }
</style>
