import { createI18n } from "@inlang/paraglide-sveltekit"
import { match as int } from "$lib/params/int"
import * as runtime from "$lib/paraglide/runtime.js"

export const i18n = createI18n(runtime, {
    defaultLanguageTag: "pl",
    pathnames: {
        '/medical-facility': {
            pl: '/dla-placowki',
            en: '/medical-facility',
            uk: '/dlya-medychnogo-zakladu',
        },
        '/medical-facility/products/syndose': {
            pl: '/dla-placowki/produkty/syndose',
            en: '/medical-facility/products/syndose',
            uk: '/dlya-medychnogo-zakladu/produkty/syndose',
        },
        '/medical-facility/products/wow': {
            pl: '/dla-placowki/produkty/wow',
            en: '/medical-facility/products/wow',
            uk: '/dlya-medychnogo-zakladu/produkty/wow',
        },
        '/medical-facility/products/wdm': {
            pl: '/dla-placowki/produkty/wdm',
            en: '/medical-facility/products/wdm',
            uk: '/dlya-medychnogo-zakladu/produkty/wdm',
        },
        '/medical-facility/products': {
            pl: '/dla-placowki/produkty',
            en: '/medical-facility/products',
            uk: '/dlya-medychnogo-zakladu/produkty',
        },
        '/contact': {
            pl: '/kontakt',
            en: '/contact-us',
            uk: '/kontakt',
        },
        '/knowledge/[slug]': {
            pl: '/poradnik-zdrowia/[slug]',
            en: '/knowledge/[slug]',
            uk: '/posibnyk-zdorovya/[slug]',
        },
        '/knowledge': {
            pl: '/poradnik-zdrowia',
            en: '/knowledge',
            uk: '/posibnyk-zdorovya',
        },
        '/legal': {
            pl: '/regulamin',
            en: '/legal',
            uk: '/pravyla',
        },
        '/privacy-policy': {
            pl: '/polityka-prywatnosci',
            en: '/privacy-policy',
            uk: '/privacy-policy',
        },
        '/articles': {
            pl: '/artykuly',
            en: '/articles',
            uk: '/statti',
        },    
	},
    matchers: { int },
    prefixDefaultLanguage: "never",
    textDirection: {
        pl: "ltr",
        en: "ltr",
        uk: "ltr"
    },
})
