@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
    view-transition-name: none;
}

body {
    overflow-x: hidden;
}

html {
	scroll-behavior: smooth;
    overflow-x:hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-family: '<PERSON><PERSON>', 'Rubik';
}

.no-scroll {
    overflow: hidden;
}

html.a11yFontSize body { font-size: 1.15rem }
html.a11yDyslexia { @apply tracking-wider }
html.a11yContrast { @apply contrast-150 }
html.a11yLinks a, html.a11yLinks button { @apply outline outline-1 outline-red-500}

html.a11yMotion {
    @apply transition-none animate-none;
    view-transition-name: none;
}
@media (prefers-reduced-motion: reduce) {
    html {
        @apply transition-none animate-none;
        view-transition-name: none;
    }
}

.btn {
    @apply no-underline border-2 border-blue-300 rounded-lg py-2 px-4 text-blue-300 inline-block hover:bg-blue-500 hover:border-blue-500 hover:text-white transition-all;

    &.btn-destructive {
        @apply border-red-500 hover:bg-red-500 text-red-500 hover:text-white
    }

    &.btn-fill {
        @apply bg-blue-500 text-white border-blue-500
    }
}

.container {
    @apply px-8;
}

@media screen and (max-width: 640px) {
    .container {
        @apply px-4;
    }
}

html[lang=uk], html[lang=uk] #cc-main {
    font-family: 'Rubik' !important;
}
